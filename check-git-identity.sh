#!/bin/bash

# Git Identity Checker
# Helps verify which GitHub account is being used for commits and pushes

echo "=== Git Identity Check ==="
echo

# Check local Git config
echo "📝 Local Git Config:"
echo "  User Name: $(git config --local user.name 2>/dev/null || echo 'Not set')"
echo "  User Email: $(git config --local user.email 2>/dev/null || echo 'Not set')"
echo

# Check global Git config (fallback)
echo "🌍 Global Git Config (fallback):"
echo "  User Name: $(git config --global user.name 2>/dev/null || echo 'Not set')"
echo "  User Email: $(git config --global user.email 2>/dev/null || echo 'Not set')"
echo

# Check remote URLs
echo "🔗 Remote URLs:"
git remote -v 2>/dev/null || echo "No remotes configured"
echo

# Check SSH connection for different accounts
echo "🔑 SSH Connection Test:"
echo "Testing default SSH (should be Gy-Hu):"
ssh -T ************** 2>&1 | grep "Hi" || echo "Connection failed"

echo "Testing Gary-oak-Star SSH:"
ssh -T -i ~/.ssh/id_ed25519_gary_oak ************** 2>&1 | grep "Hi" || echo "Connection failed"
echo

# Show recent commit author
echo "📋 Recent Commit Author:"
git log -1 --pretty=format:"  %an <%ae>" 2>/dev/null || echo "No commits found"
echo
echo

# Recommendations
echo "💡 Recommendations:"
current_user=$(git config --local user.name 2>/dev/null)
current_email=$(git config --local user.email 2>/dev/null)

if [[ "$current_user" == "Gary-oak-Star" && "$current_email" == "<EMAIL>" ]]; then
    echo "  ✅ Configured for Gary-oak-Star account"
    echo "  📤 Use: GIT_SSH_COMMAND=\"ssh -i ~/.ssh/id_ed25519_gary_oak\" git push"
elif [[ "$current_user" == "Gary Hu" || "$current_email" == "<EMAIL>" ]]; then
    echo "  ✅ Configured for Gy-Hu account"
    echo "  📤 Use: git push (default SSH key)"
else
    echo "  ⚠️  Git identity not clearly configured"
    echo "  🔧 Set for Gary-oak-Star: git config --local user.name 'Gary-oak-Star' && git config --local user.email '<EMAIL>'"
    echo "  🔧 Set for Gy-Hu: git config --local user.name 'Gary Hu' && git config --local user.email '<EMAIL>'"
fi
